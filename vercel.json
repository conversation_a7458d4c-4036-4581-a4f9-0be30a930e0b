{"buildCommand": "pnpm build", "outputDirectory": "dist", "framework": "vite", "functions": {"api/index.js": {"maxDuration": 30}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/index.js"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*\\.html|/)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}